# Recurring Lesson Cleanup Enhancement

## Overview

This document describes the enhancement to the lesson cleanup functionality to properly handle recurring lessons. The original cleanup logic only considered individual lesson end times, which was inappropriate for recurring lessons that may have future occurrences.

## Problem Statement

The original `LessonCleanupService` used a simple approach:
```sql
DELETE FROM Lessons
WHERE EndTime < DATEADD(day, -@OlderThanDays, GETUTCDATE())
```

This approach had a critical flaw: it would delete recurring lessons based on their original start/end time, even if the recurring series had future occurrences that hadn't ended yet.

For example:
- A weekly lesson starting January 1st with COUNT=10 would have its last occurrence on March 5th
- The original logic would delete this lesson on February 1st (30 days after the original end time)
- This would incorrectly remove lessons that still had valid future occurrences

## Solution

The enhanced cleanup logic now:

1. **Separates recurring and non-recurring lessons**
2. **For non-recurring lessons**: Uses the original logic (delete if EndTime + retention period has passed)
3. **For recurring lessons**: Calculates the actual last occurrence date and only deletes after that date + retention period

## Implementation Details

### Enhanced Methods

#### `PermanentlyDeleteAllLessonsAsync(int olderThanDays)`
- Splits cleanup into two phases: non-recurring and recurring lessons
- Uses helper methods to calculate last occurrence dates for recurring lessons
- Only deletes recurring lessons when their entire series has ended + retention period

#### `GetAllLessonsCountAsync(int olderThanDays)`
- Updated to provide accurate counts using the same logic as deletion
- Helps with monitoring and reporting

#### `GetRecurringLessonsForCleanupAsync(SqlConnection connection, int olderThanDays)`
- Retrieves recurring lessons that might be eligible for cleanup
- Filters by lessons that started before the retention cutoff date

#### `CalculateLastOccurrenceDate(Lesson lesson)`
- Parses RecurrenceRule to determine the last occurrence
- Handles three scenarios:
  - **UNTIL date**: Uses the specified end date
  - **COUNT**: Calculates the last occurrence based on frequency and count
  - **Infinite recurrence**: Returns DateTime.MaxValue (never delete)

#### `CalculateLastOccurrenceFromCount(Lesson lesson, int count)`
- Calculates the exact date of the last occurrence for COUNT-based recurrence
- Supports DAILY, WEEKLY, MONTHLY, and YEARLY frequencies
- Accounts for INTERVAL values (e.g., every 2 weeks)
- Returns the end time of the last occurrence

### Supported Recurrence Patterns

The implementation supports RFC 5545 compliant recurrence rules:

#### UNTIL-based Rules
```
FREQ=DAILY;INTERVAL=1;UNTIL=20240310T110000
```
- Uses the UNTIL date as the last occurrence

#### COUNT-based Rules
```
FREQ=WEEKLY;INTERVAL=1;COUNT=10
FREQ=DAILY;INTERVAL=2;COUNT=5
FREQ=MONTHLY;INTERVAL=1;COUNT=6
```
- Calculates last occurrence: start + (count-1) * interval * frequency_unit

#### Infinite Rules
```
FREQ=WEEKLY;INTERVAL=1
```
- Never deleted (returns DateTime.MaxValue)

## Testing

The implementation was thoroughly tested with various recurrence patterns:

1. **Daily Recurrence**: 10 daily lessons → Last occurrence calculated correctly
2. **Weekly Recurrence**: 5 weekly lessons → Last occurrence calculated correctly  
3. **Monthly Recurrence**: 3 monthly lessons → Last occurrence calculated correctly
4. **UNTIL Date**: Lessons until specific date → UNTIL date used correctly
5. **Infinite Recurrence**: No COUNT/UNTIL → Never deleted (DateTime.MaxValue)

All test cases passed, confirming the logic correctly handles different recurrence scenarios.

## Benefits

1. **Accurate Cleanup**: Only deletes recurring lessons after their entire series has ended
2. **Data Integrity**: Prevents premature deletion of active recurring lesson series
3. **Flexible**: Handles all common recurrence patterns (daily, weekly, monthly, yearly)
4. **Safe**: Conservative approach for infinite or unparseable recurrence rules
5. **Backward Compatible**: Non-recurring lessons continue to use the original logic

## Configuration

The cleanup behavior is controlled by the same configuration settings:

- `LESSON_CLEANUP_RETENTION_DAYS` (default: 30 days)
- `LESSON_CLEANUP_INTERVAL_HOURS` (default: 24 hours)

For recurring lessons, the retention period is applied after the calculated last occurrence date.

## Example Scenarios

### Scenario 1: Weekly Piano Lessons
- **Start**: January 1, 2024, 2:00 PM - 3:00 PM
- **Rule**: `FREQ=WEEKLY;INTERVAL=1;COUNT=8`
- **Last Occurrence**: February 19, 2024, 3:00 PM (end time of 8th lesson)
- **Cleanup Date**: March 20, 2024 (last occurrence + 30 days retention)

### Scenario 2: Monthly Guitar Lessons
- **Start**: January 15, 2024, 4:00 PM - 5:00 PM  
- **Rule**: `FREQ=MONTHLY;INTERVAL=1;COUNT=6`
- **Last Occurrence**: June 15, 2024, 5:00 PM (end time of 6th lesson)
- **Cleanup Date**: July 15, 2024 (last occurrence + 30 days retention)

### Scenario 3: Infinite Violin Lessons
- **Start**: January 1, 2024, 10:00 AM - 11:00 AM
- **Rule**: `FREQ=WEEKLY;INTERVAL=1` (no COUNT or UNTIL)
- **Last Occurrence**: Never (DateTime.MaxValue)
- **Cleanup Date**: Never (infinite series preserved)

## Error Handling

The implementation includes robust error handling:

- **Invalid RecurrenceRule**: Treated as infinite series (never deleted)
- **Unparseable dates**: Treated as infinite series (never deleted)
- **Missing frequency**: Treated as infinite series (never deleted)
- **Database errors**: Logged and re-thrown for proper error handling

This conservative approach ensures that lessons are never accidentally deleted due to parsing errors.
