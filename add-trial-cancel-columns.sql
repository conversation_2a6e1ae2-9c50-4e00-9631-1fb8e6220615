USE [MusicSchool]

-- Add IsTrial column for trial lessons
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Lessons]') AND name = 'IsTrial')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD [IsTrial] [bit] NOT NULL DEFAULT 0;
    PRINT 'Added IsTrial column to Lessons table';
END
ELSE
BEGIN
    PRINT 'IsTrial column already exists in Lessons table';
END

-- Add IsCancelled column for cancelled lessons
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Lessons]') AND name = 'IsCancelled')
BEGIN
    ALTER TABLE [dbo].[Lessons]
    ADD [IsCancelled] [bit] NOT NULL DEFAULT 0;
    PRINT 'Added IsCancelled column to Lessons table';
END
ELSE
BEGIN
    PRINT 'IsCancelled column already exists in Lessons table';
END
