# PowerShell script to run the trial and cancel migration
# This script adds IsTrial and IsCancelled columns to the Lessons table

$connectionString = "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;"
$sqlFile = "ShiningCMusicApi\SQL\Add_Recurring_Events_Support.sql"

Write-Host "Running trial and cancel migration..." -ForegroundColor Green

try {
    # Read the SQL file
    $sqlContent = Get-Content -Path $sqlFile -Raw
    
    # Create SQL connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    # Execute the SQL
    $command = New-Object System.Data.SqlClient.SqlCommand($sqlContent, $connection)
    $command.ExecuteNonQuery()
    
    Write-Host "Migration completed successfully!" -ForegroundColor Green
    
    $connection.Close()
}
catch {
    Write-Host "Error running migration: $($_.Exception.Message)" -ForegroundColor Red
}
